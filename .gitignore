.venv
__pycache__
.DS_Store
testing/
.aider*
package.json
package-lock.json
node_modules/
venv
secret_key.txt
logs/
uploads/
instance/
debug_responses/
flask_session/
answers.txt
chemans.pdf
chemqp.pdf
extracted_images.json
groq_raw_responses.txt
groq_structured_answers.json
groq_structured_data.json
temp_files/
myenv/
gemini_questions_fixed.txt
answers.txt
answers_fixed.txt
questions.txt
questions_fixed.txt
.env
TODO.md
test*