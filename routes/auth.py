import os
from supabase import create_client, Client
from flask import request, render_template, redirect, url_for, flash, session
from gotrue.errors import AuthApiError
from models import User, db
from .utils import login_required, app_logger, user_logger, error_logger, update_user_activity


def register_auth_routes(app, db, _):
    """Register authentication routes using Supabase magic link authentication."""

    # Initialize Supabase client
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    SUPABASE_KEY = os.getenv('SUPABASE_KEY')

    if not SUPABASE_URL or not SUPABASE_KEY:
        raise ValueError("SUPABASE_URL and SUPABASE_KEY environment variables are required")

    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

    @app.route("/login", methods=['GET', 'POST'])
    def login():
        """Handle login with magic link authentication."""
        # Redirect if already logged in
        if 'user_id' in session:
            return redirect(url_for('dashboard'))

        if request.method == 'POST':
            email = request.form.get('email', '').strip().lower()

            if not email:
                flash('Email address is required.', 'warning')
                return render_template('login.html')

            try:
                # Send magic link via Supabase
                supabase.auth.sign_in_with_otp({
                    'email': email,
                    'options': {
                        'email_redirect_to': f"{request.host_url}auth/callback"
                    }
                })

                app_logger.info(f"Magic link sent to {email}")
                flash(f'A magic link has been sent to {email}. Please check your email and click the link to sign in.', 'success')
                return render_template('login.html', email_sent=True, email=email)

            except AuthApiError as e:
                error_logger.error(f"Supabase auth error for {email}: {e.message}")
                flash('An error occurred while sending the magic link. Please try again.', 'error')
                return render_template('login.html')
            except Exception as e:
                error_logger.exception(f"Unexpected error during login for {email}: {str(e)}")
                flash('An unexpected error occurred. Please try again.', 'error')
                return render_template('login.html')

        # GET request - show login form
        return render_template('login.html')

    @app.route("/register", methods=['GET', 'POST'])
    def register():
        """Handle registration with magic link authentication."""
        # Redirect if already logged in
        if 'user_id' in session:
            return redirect(url_for('dashboard'))

        if request.method == 'POST':
            email = request.form.get('email', '').strip().lower()
            username = request.form.get('username', '').strip()

            if not email or not username:
                flash('Email and username are required.', 'warning')
                return render_template('register.html')

            # Validate username
            if len(username) < 3:
                flash('Username must be at least 3 characters long.', 'warning')
                return render_template('register.html')

            if not username.replace('_', '').replace('-', '').isalnum():
                flash('Username can only contain letters, numbers, hyphens, and underscores.', 'warning')
                return render_template('register.html')

            # Check if email or username already exists
            existing_user = User.query.filter(
                (User.email == email) | (User.username == username)
            ).first()

            if existing_user:
                if existing_user.email == email:
                    flash('An account with this email already exists. Please sign in instead.', 'warning')
                else:
                    flash('This username is already taken. Please choose a different one.', 'warning')
                return render_template('register.html')

            try:
                # Send magic link for registration
                supabase.auth.sign_in_with_otp({
                    'email': email,
                    'options': {
                        'email_redirect_to': f"{request.host_url}auth/callback?username={username}&action=register"
                    }
                })

                app_logger.info(f"Registration magic link sent to {email} for username {username}")
                flash(f'A magic link has been sent to {email}. Please check your email and click the link to complete your registration.', 'success')
                return render_template('register.html', email_sent=True, email=email, username=username)

            except AuthApiError as e:
                error_logger.error(f"Supabase auth error during registration for {email}: {e.message}")
                flash('An error occurred while sending the magic link. Please try again.', 'error')
                return render_template('register.html')
            except Exception as e:
                error_logger.exception(f"Unexpected error during registration for {email}: {str(e)}")
                flash('An unexpected error occurred. Please try again.', 'error')
                return render_template('register.html')

        # GET request - show registration form
        return render_template('register.html')

    @app.route("/auth/callback")
    def auth_callback():
        """Handle magic link callback and complete authentication."""
        try:
            # Get the current user from Supabase session
            user_response = supabase.auth.get_user()
            supabase_user = user_response.user

            if not supabase_user or not supabase_user.email:
                flash('Authentication failed. Please try again.', 'error')
                return redirect(url_for('login'))

            email = supabase_user.email.lower()
            action = request.args.get('action', 'login')
            username = request.args.get('username')

            # Check if this is a registration callback
            if action == 'register' and username:
                # Check if user already exists (shouldn't happen, but safety check)
                existing_user = User.query.filter(
                    (User.email == email) | (User.username == username)
                ).first()

                if existing_user:
                    flash('An account with this email or username already exists.', 'warning')
                    return redirect(url_for('login'))

                # Create new user with random password (since we use Supabase for auth)
                import secrets
                user = User(username=username, email=email)
                user.set_password(secrets.token_urlsafe(32))  # Random password
                user.role = 'member'  # Default role

                db.session.add(user)
                db.session.commit()

                user_logger.info(f"New user registered: {username} ({email})")

                # Log the user in
                session['user_id'] = user.id
                session['username'] = user.username
                session.permanent = True

                update_user_activity(user.id)
                flash('Registration successful! Welcome to Vast.', 'success')

                # Redirect to onboarding for new users
                return redirect(url_for('onboarding'))

            else:
                # Login callback - find existing user
                user = User.query.filter_by(email=email).first()

                if not user:
                    flash('No account found with this email. Please register first.', 'warning')
                    return redirect(url_for('register'))

                # Log the user in
                session['user_id'] = user.id
                session['username'] = user.username
                session.permanent = True

                # Check if this is the first login of the day
                first_login_of_day = user.is_first_login_of_day()
                if first_login_of_day:
                    session['show_confetti'] = True
                    user.update_login_date()
                    db.session.commit()
                    app_logger.info(f"First login of the day for user {user.username}")

                update_user_activity(user.id)
                user_logger.info(f"User {user.username} logged in successfully via magic link")
                flash('Login successful!', 'success')

                # Check if user needs onboarding
                if not user.onboarding_completed:
                    return redirect(url_for('onboarding'))

                # Redirect to intended page or dashboard
                next_page = request.args.get('next')
                if next_page and next_page.startswith('/'):
                    return redirect(next_page)
                return redirect(url_for('dashboard'))

        except Exception as e:
            error_logger.exception(f"Error during auth callback: {str(e)}")
            flash('An error occurred during authentication. Please try again.', 'error')
            return redirect(url_for('login'))

    @app.route("/logout")
    @login_required
    def logout():
        """Handle user logout."""
        user_id = session.get('user_id')
        username = session.get('username', 'Unknown')

        # Update activity one last time before logout
        if user_id:
            update_user_activity(user_id)

        try:
            # Sign out from Supabase
            supabase.auth.sign_out()
            app_logger.info(f"User {username} signed out from Supabase")
        except Exception as e:
            error_logger.warning(f"Error signing out from Supabase: {str(e)}")

        # Clear the session
        session.clear()

        user_logger.info(f"User {username} (ID: {user_id}) logged out")
        flash("You have been logged out.", "info")
        return redirect(url_for('login'))