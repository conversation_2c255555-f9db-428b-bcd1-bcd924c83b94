import os
from supabase import create_client, Client
from flask import request, render_template, redirect, url_for, flash, session
from gotrue.errors import AuthApiError
from models import User, db
from .utils import login_required, app_logger, user_logger, error_logger, update_user_activity


def register_auth_routes(app, db, _):
    """Register authentication routes using Supabase magic link authentication."""

    # Initialize Supabase client
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    SUPABASE_KEY = os.getenv('SUPABASE_KEY')

    if not SUPABASE_URL or not SUPABASE_KEY:
        raise ValueError("SUPABASE_URL and SUPABASE_KEY environment variables are required")

    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

    @app.route("/login", methods=['GET', 'POST'])
    def login():
        """Handle login - just email entry, automatic login for verified emails."""
        # Redirect if already logged in
        if 'user_id' in session:
            return redirect(url_for('dashboard'))

        if request.method == 'POST':
            email = request.form.get('email', '').strip().lower()

            if not email:
                flash('Email address is required.', 'warning')
                return render_template('login.html')

            # Check if user exists locally
            user = User.query.filter_by(email=email).first()

            if not user:
                flash('No account found with this email. Please sign up first.', 'warning')
                return render_template('login.html')

            try:
                # For verified emails, log them in directly
                session['user_id'] = user.id
                session['username'] = user.username
                session.permanent = True

                # Check if this is the first login of the day
                first_login_of_day = user.is_first_login_of_day()
                if first_login_of_day:
                    session['show_confetti'] = True
                    user.update_login_date()
                    db.session.commit()
                    app_logger.info(f"First login of the day for user {user.username}")

                update_user_activity(user.id)
                user_logger.info(f"User {user.username} logged in successfully")
                flash('Login successful!', 'success')

                # Check if user needs onboarding
                if not user.onboarding_completed:
                    return redirect(url_for('onboarding'))

                # Redirect to intended page or dashboard
                next_page = request.args.get('next')
                if next_page and next_page.startswith('/'):
                    return redirect(next_page)
                return redirect(url_for('dashboard'))

            except Exception as e:
                error_logger.exception(f"Unexpected error during login for {email}: {str(e)}")
                flash('An unexpected error occurred. Please try again.', 'error')
                return render_template('login.html')

        # GET request - show login form
        return render_template('login.html')

    @app.route("/register", methods=['GET', 'POST'])
    def register():
        """Handle registration with email + OTP verification."""
        # Redirect if already logged in
        if 'user_id' in session:
            return redirect(url_for('dashboard'))

        if request.method == 'POST':
            email = request.form.get('email', '').strip().lower()
            otp = request.form.get('otp', '').strip()

            if not email:
                flash('Email address is required.', 'warning')
                return render_template('register.html')

            # Check if email already exists
            existing_user = User.query.filter_by(email=email).first()
            if existing_user:
                flash('An account with this email already exists. Please sign in instead.', 'warning')
                return render_template('register.html')

            if not otp:
                # Step 1: Send OTP
                try:
                    supabase.auth.sign_in_with_otp({
                        'email': email,
                        'options': {
                            'should_create_user': True
                        }
                    })

                    app_logger.info(f"OTP sent to {email} for registration")
                    flash(f'A verification code has been sent to {email}. Please enter the code below.', 'success')
                    return render_template('register.html', email=email, otp_sent=True)

                except AuthApiError as e:
                    error_logger.error(f"Supabase auth error during OTP send for {email}: {e.message}")
                    flash('An error occurred while sending the verification code. Please try again.', 'error')
                    return render_template('register.html')
                except Exception as e:
                    error_logger.exception(f"Unexpected error during OTP send for {email}: {str(e)}")
                    flash('An unexpected error occurred. Please try again.', 'error')
                    return render_template('register.html')
            else:
                # Step 2: Verify OTP and create user
                try:
                    # Verify OTP with Supabase
                    verify_response = supabase.auth.verify_otp({
                        'email': email,
                        'token': otp,
                        'type': 'email'
                    })

                    if verify_response.user:
                        # Generate username from email
                        base_username = email.split('@')[0]
                        username = base_username

                        # Make sure username is unique
                        counter = 1
                        while User.query.filter_by(username=username).first():
                            username = f"{base_username}{counter}"
                            counter += 1

                        # Create local user with random password
                        import secrets
                        user = User(username=username, email=email)
                        user.set_password(secrets.token_urlsafe(32))
                        user.role = 'member'

                        db.session.add(user)
                        db.session.commit()

                        user_logger.info(f"New user registered: {username} ({email})")

                        # Log the user in
                        session['user_id'] = user.id
                        session['username'] = user.username
                        session.permanent = True

                        update_user_activity(user.id)
                        flash('Registration successful! Welcome to Vast.', 'success')

                        # Redirect to onboarding for new users
                        return redirect(url_for('onboarding'))
                    else:
                        flash('Invalid verification code. Please try again.', 'error')
                        return render_template('register.html', email=email, otp_sent=True)

                except AuthApiError as e:
                    error_logger.error(f"Supabase auth error during OTP verification for {email}: {e.message}")
                    flash('Invalid verification code. Please try again.', 'error')
                    return render_template('register.html', email=email, otp_sent=True)
                except Exception as e:
                    error_logger.exception(f"Unexpected error during registration for {email}: {str(e)}")
                    flash('An unexpected error occurred. Please try again.', 'error')
                    return render_template('register.html')

        # GET request - show registration form
        return render_template('register.html')



    @app.route("/logout")
    @login_required
    def logout():
        """Handle user logout."""
        user_id = session.get('user_id')
        username = session.get('username', 'Unknown')

        # Update activity one last time before logout
        if user_id:
            update_user_activity(user_id)

        try:
            # Sign out from Supabase
            supabase.auth.sign_out()
            app_logger.info(f"User {username} signed out from Supabase")
        except Exception as e:
            error_logger.warning(f"Error signing out from Supabase: {str(e)}")

        # Clear the session
        session.clear()

        user_logger.info(f"User {username} (ID: {user_id}) logged out")
        flash("You have been logged out.", "info")
        return redirect(url_for('login'))