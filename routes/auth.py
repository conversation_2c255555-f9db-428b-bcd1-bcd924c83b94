import os
import secrets
from supabase import create_client, Client
from flask import request, render_template, redirect, url_for, flash, session
from gotrue.errors import AuthApiError
from models import User, db
from .utils import login_required, app_logger, user_logger, error_logger, update_user_activity

def load_admin_emails():
    """Load admin emails from admin.txt file."""
    admin_emails = set()
    try:
        if os.path.exists('admin.txt'):
            with open('admin.txt', 'r') as f:
                for line in f:
                    email = line.strip().lower()
                    # Skip empty lines, comments, and invalid emails
                    if email and not email.startswith('#') and '@' in email:
                        admin_emails.add(email)
            app_logger.info(f"Loaded {len(admin_emails)} admin emails from admin.txt")
        else:
            app_logger.warning("admin.txt file not found")
    except Exception as e:
        error_logger.error(f"Error loading admin emails: {str(e)}")
    return admin_emails

def validate_email(email, admin_emails):
    email = email.strip().lower()
    if email.endswith('.edu.sg') or email in admin_emails:
        return True
    
    return False

def register_auth_routes(app, db, session, supabase_client):
    admin_emails = load_admin_emails()
    
    @app.route('/login', methods=['GET', 'POST'])
    def login():
        pass