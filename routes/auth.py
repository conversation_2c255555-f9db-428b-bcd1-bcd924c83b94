import os
import secrets
from supabase import create_client, Client
from flask import request, render_template, redirect, url_for, flash, session
from gotrue.errors import AuthApiError
from models import User, db
from .utils import login_required, app_logger, user_logger, error_logger, update_user_activity


def load_admin_emails():
    """Load admin emails from admin.txt file."""
    admin_emails = set()
    try:
        if os.path.exists('admin.txt'):
            with open('admin.txt', 'r') as f:
                for line in f:
                    email = line.strip().lower()
                    if email and '@' in email:
                        admin_emails.add(email)
            app_logger.info(f"Loaded {len(admin_emails)} admin emails from admin.txt")
        else:
            app_logger.warning("admin.txt file not found")
    except Exception as e:
        error_logger.error(f"Error loading admin emails: {str(e)}")
    return admin_emails


def register_auth_routes(app, db, _):
    """Register authentication routes using email/password with OTP verification for registration."""

    # Initialize Supabase client
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    SUPABASE_KEY = os.getenv('SUPABASE_KEY')

    if not SUPABASE_URL or not SUPABASE_KEY:
        raise ValueError("SUPABASE_URL and SUPABASE_KEY environment variables are required")

    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

    # Load admin emails
    admin_emails = load_admin_emails()

    @app.route("/login", methods=['GET', 'POST'])
    def login():
        """Handle login with email and password."""
        # Redirect if already logged in
        if 'user_id' in session:
            return redirect(url_for('dashboard'))

        if request.method == 'POST':
            email = request.form.get('email', '').strip().lower()
            password = request.form.get('password', '').strip()

            if not email or not password:
                flash('Email and password are required.', 'warning')
                return render_template('login.html')

            # Check if user exists locally
            user = User.query.filter_by(email=email).first()

            if not user:
                flash('Invalid email or password.', 'error')
                return render_template('login.html')

            # Verify password
            if not user.check_password(password):
                flash('Invalid email or password.', 'error')
                error_logger.warning(f"Failed login attempt for {email} - incorrect password")
                return render_template('login.html')

            try:
                # Successful login
                session['user_id'] = user.id
                session['username'] = user.username
                session.permanent = True

                # Check if this is the first login of the day
                first_login_of_day = user.is_first_login_of_day()
                if first_login_of_day:
                    session['show_confetti'] = True
                    user.update_login_date()
                    db.session.commit()
                    app_logger.info(f"First login of the day for user {user.username}")

                update_user_activity(user.id)
                user_logger.info(f"User {user.username} logged in successfully")
                flash('Login successful!', 'success')

                # Check if user needs onboarding
                if not user.onboarding_completed:
                    return redirect(url_for('onboarding'))

                # Redirect to intended page or dashboard
                next_page = request.args.get('next')
                if next_page and next_page.startswith('/'):
                    return redirect(next_page)
                return redirect(url_for('dashboard'))

            except Exception as e:
                error_logger.exception(f"Unexpected error during login for {email}: {str(e)}")
                flash('An unexpected error occurred. Please try again.', 'error')
                return render_template('login.html')

        # GET request - show login form
        return render_template('login.html')

    @app.route("/register", methods=['GET', 'POST'])
    def register():
        """Handle registration with email + OTP verification + password creation."""
        # Redirect if already logged in
        if 'user_id' in session:
            return redirect(url_for('dashboard'))

        if request.method == 'POST':
            email = request.form.get('email', '').strip().lower()
            otp = request.form.get('otp', '').strip()
            password = request.form.get('password', '').strip()
            confirm_password = request.form.get('confirm_password', '').strip()

            if not email:
                flash('Email address is required.', 'warning')
                return render_template('register.html')

            # Check if email already exists
            existing_user = User.query.filter_by(email=email).first()
            if existing_user:
                flash('An account with this email already exists. Please sign in instead.', 'warning')
                return render_template('register.html')

            if not otp:
                # Step 1: Send OTP - use a simpler approach
                try:
                    # Always try to send OTP with create user option
                    # If user exists, Supabase will still send OTP
                    supabase.auth.sign_in_with_otp({
                        'email': email
                    })

                    app_logger.info(f"OTP sent to {email} for registration")
                    flash(f'A verification code has been sent to {email}. Please enter the code and create a password.', 'success')
                    return render_template('register.html', email=email, otp_sent=True)

                except AuthApiError as e:
                    # Handle specific Supabase errors
                    if "Database error saving new user" in e.message:
                        # User likely already exists, but we can still proceed with OTP
                        app_logger.warning(f"User {email} may already exist in Supabase, proceeding with OTP")
                        flash(f'A verification code has been sent to {email}. Please enter the code and create a password.', 'success')
                        return render_template('register.html', email=email, otp_sent=True)
                    elif "For security purposes" in e.message:
                        # Rate limiting
                        flash('Please wait before requesting another verification code.', 'warning')
                        return render_template('register.html')
                    else:
                        error_logger.error(f"Supabase auth error during OTP send for {email}: {e.message}")
                        flash('An error occurred while sending the verification code. Please try again.', 'error')
                        return render_template('register.html')
                except Exception as e:
                    error_logger.exception(f"Unexpected error during OTP send for {email}: {str(e)}")
                    flash('An unexpected error occurred. Please try again.', 'error')
                    return render_template('register.html')
            else:
                # Step 2: Verify OTP and create user with password
                if not password or not confirm_password:
                    flash('Password and password confirmation are required.', 'warning')
                    return render_template('register.html', email=email, otp_sent=True)

                if password != confirm_password:
                    flash('Passwords do not match.', 'warning')
                    return render_template('register.html', email=email, otp_sent=True)

                if len(password) < 6:
                    flash('Password must be at least 6 characters long.', 'warning')
                    return render_template('register.html', email=email, otp_sent=True)

                try:
                    # First, verify OTP with Supabase
                    verify_response = supabase.auth.verify_otp({
                        'email': email,
                        'token': otp,
                        'type': 'email'
                    })

                    if verify_response.user:
                        # Check if user already exists locally (shouldn't happen, but safety check)
                        existing_local_user = User.query.filter_by(email=email).first()
                        if existing_local_user:
                            flash('An account with this email already exists. Please sign in instead.', 'warning')
                            return redirect(url_for('login'))

                        # Now create/update the user in Supabase with the actual password
                        try:
                            # Update the user's password in Supabase
                            supabase.auth.update_user({
                                'password': password
                            })
                            app_logger.info(f"Updated Supabase user password for {email}")
                        except Exception as e:
                            error_logger.warning(f"Could not update Supabase password for {email}: {str(e)}")
                            # Continue anyway - we'll store the password locally

                        # Check if email is in admin list
                        is_admin = email in admin_emails
                        role = 'admin' if is_admin else 'member'

                        # Create local user with the same password
                        # Use email as both username and name for simplicity
                        user = User(username=email, email=email)
                        user.set_password(password)  # Use the actual user password, not random
                        user.role = role

                        db.session.add(user)
                        db.session.commit()

                        user_logger.info(f"New user registered: {email} (role: {role})")

                        # Log the user in
                        session['user_id'] = user.id
                        session['username'] = user.username
                        session.permanent = True

                        update_user_activity(user.id)

                        if is_admin:
                            flash('Registration successful! You have been granted admin privileges.', 'success')
                        else:
                            flash('Registration successful! Welcome to Vast.', 'success')

                        # Redirect to onboarding for new users
                        return redirect(url_for('onboarding'))
                    else:
                        flash('Invalid verification code. Please try again.', 'error')
                        return render_template('register.html', email=email, otp_sent=True)

                except AuthApiError as e:
                    error_logger.error(f"Supabase auth error during OTP verification for {email}: {e.message}")
                    flash('Invalid verification code. Please try again.', 'error')
                    return render_template('register.html', email=email, otp_sent=True)
                except Exception as e:
                    error_logger.exception(f"Unexpected error during registration for {email}: {str(e)}")
                    flash('An unexpected error occurred. Please try again.', 'error')
                    return render_template('register.html')

        # GET request - show registration form
        return render_template('register.html')



    @app.route("/logout")
    @login_required
    def logout():
        """Handle user logout."""
        user_id = session.get('user_id')
        username = session.get('username', 'Unknown')

        # Update activity one last time before logout
        if user_id:
            update_user_activity(user_id)

        try:
            # Sign out from Supabase
            supabase.auth.sign_out()
            app_logger.info(f"User {username} signed out from Supabase")
        except Exception as e:
            error_logger.warning(f"Error signing out from Supabase: {str(e)}")

        # Clear the session
        session.clear()

        user_logger.info(f"User {username} (ID: {user_id}) logged out")
        flash("You have been logged out.", "info")
        return redirect(url_for('login'))