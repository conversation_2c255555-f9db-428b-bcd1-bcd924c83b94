{% extends "base.html" %}

{% block content %}
<div class="min-h-[80vh] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
        <!-- Header -->
        <div class="text-center">
            <h1 class="text-4xl font-bold tracking-tight text-gray-900">Join Vast</h1>
            <p class="mt-2 text-sm text-gray-600">
                Create your account and start your learning journey
            </p>
        </div>

        <!-- Registration Form -->
        <div class="mt-8 bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10 transform transition-all hover:scale-[1.01] duration-300">
            {% if email_sent %}
                <!-- Magic Link Sent Message -->
                <div class="text-center space-y-4">
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100">
                        <i class="fas fa-envelope text-green-600 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900">Magic Link Sent!</h3>
                    <p class="text-sm text-gray-600">
                        We've sent a magic link to <strong>{{ email }}</strong> for username <strong>{{ username }}</strong>.
                        Please check your email and click the link to complete your registration.
                    </p>
                    <div class="mt-6">
                        <a href="{{ url_for('register') }}" class="text-sm font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200">
                            ← Back to registration
                        </a>
                    </div>
                </div>
            {% else %}
                <!-- Registration Form -->
                <form method="POST" action="/register" class="space-y-6">
                    <div class="space-y-6">
                        <div>
                            <label for="username" class="block text-sm font-medium leading-6 text-gray-900">
                                Username
                            </label>
                            <div class="mt-2 relative rounded-md shadow-sm">
                                <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                                    <i class="fas fa-user text-gray-400"></i>
                                </div>
                                <input type="text" id="username" name="username" required
                                       class="block w-full rounded-md border-0 py-2 pl-10 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 transition-colors duration-200"
                                       placeholder="Choose a username">
                            </div>
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium leading-6 text-gray-900">
                                Email Address
                            </label>
                            <div class="mt-2 relative rounded-md shadow-sm">
                                <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                                    <i class="fas fa-envelope text-gray-400"></i>
                                </div>
                                <input type="email" id="email" name="email" required
                                       class="block w-full rounded-md border-0 py-2 pl-10 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 transition-colors duration-200"
                                       placeholder="Enter your email address">
                            </div>
                            <p class="mt-2 text-xs text-gray-500">
                                <i class="fas fa-magic mr-1"></i>
                                We'll send you a magic link to complete your registration securely.
                            </p>
                        </div>
                    </div>

                    <div>
                        <button type="submit"
                                class="group relative flex w-full justify-center rounded-md bg-indigo-600 px-3 py-3 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transition-all duration-200">
                            <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                                <i class="fas fa-paper-plane text-indigo-300 group-hover:text-indigo-200"></i>
                            </span>
                            Send Registration Link
                        </button>
                    </div>

                    <div class="text-center text-sm">
                        <p class="text-gray-600">
                            Already have an account?
                            <a href="{{ url_for('login') }}" class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200">
                                Sign in
                            </a>
                        </p>
                    </div>
                </form>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}