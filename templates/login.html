{% extends "base.html" %}

{% block content %}
<div class="min-h-[80vh] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
        <!-- Header -->
        <div class="text-center">
            <h1 class="text-4xl font-bold tracking-tight text-gray-900">Welcome Back</h1>
            <p class="mt-2 text-sm text-gray-600">
                Sign in to continue your learning journey
            </p>
        </div>

        <!-- Password Reset Success Message -->
        <div id="password-reset-success" class="hidden bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
            <strong class="font-bold">Success!</strong>
            <span class="block sm:inline">Your password has been reset. Please log in with your new password.</span>
        </div>

        <!-- Login Form -->
        <div class="mt-8 bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10 transform transition-all hover:scale-[1.01] duration-300">
            <form method="POST" action="/login" class="space-y-6">
                <div>
                    <label for="email" class="block text-sm font-medium leading-6 text-gray-900">
                        Email Address
                    </label>
                    <div class="mt-2 relative rounded-md shadow-sm">
                        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                            <i class="fas fa-envelope text-gray-400"></i>
                        </div>
                        <input type="email" id="email" name="email" required
                               class="block w-full rounded-md border-0 py-2 pl-10 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 transition-colors duration-200"
                               placeholder="Enter your email address">
                    </div>
                    <p class="mt-2 text-xs text-gray-500">
                        <i class="fas fa-shield-alt mr-1"></i>
                        Enter your verified email address to sign in instantly.
                    </p>
                </div>

                <div>
                    <button type="submit"
                            class="group relative flex w-full justify-center rounded-md bg-indigo-600 px-3 py-3 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transition-all duration-200">
                        <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                            <i class="fas fa-sign-in-alt text-indigo-300 group-hover:text-indigo-200"></i>
                        </span>
                        Sign In
                    </button>
                </div>

                <div class="text-center text-sm">
                    <p class="text-gray-600">
                        Don't have an account?
                        <a href="{{ url_for('register') }}" class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200">
                            Create one now
                        </a>
                    </p>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if we have a reset=success parameter in the URL
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('reset') === 'success') {
            // Show the success message
            const successMessage = document.getElementById('password-reset-success');
            if (successMessage) {
                successMessage.classList.remove('hidden');
            }
        }
    });
</script>
{% endblock %}