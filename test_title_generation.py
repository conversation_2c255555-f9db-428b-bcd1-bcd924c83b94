#!/usr/bin/env python3
"""
Test script for the automatic question title generation functionality.
"""

import sys
import os
from flask import Flask

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the Flask app and models
from app import app
from models import db, Question
from routes.ai_helpers import generate_and_update_question_title, generate_question_title

def test_title_generation():
    """Test the title generation functionality"""
    with app.app_context():
        # Test with a sample question
        sample_description = "Calculate the pH of a 0.1 M solution of acetic acid (Ka = 1.8 × 10^-5)"
        sample_parts = [
            "Write the equilibrium expression for the dissociation of acetic acid",
            "Set up an ICE table for the dissociation",
            "Calculate the concentration of H+ ions",
            "Calculate the pH of the solution"
        ]
        
        try:
            print("Testing title generation with sample data...")
            title = generate_question_title(sample_description, sample_parts)
            print(f"Generated title: '{title}'")
            print("✅ Title generation test passed!")
            
        except Exception as e:
            print(f"❌ Title generation test failed: {e}")
            return False
        
        # Test with an existing question from database
        try:
            print("\nTesting with existing questions from database...")
            questions = Question.query.limit(3).all()
            
            if not questions:
                print("No questions found in database to test with.")
                return True
            
            for question in questions:
                print(f"\nTesting question ID {question.id}:")
                print(f"Current title: '{question.title}'")
                
                # Generate new title without saving
                question_description = question.description or ""
                parts_descriptions = []
                
                if question.parts:
                    for part in question.parts:
                        if part.description:
                            parts_descriptions.append(part.description)
                
                if question_description or parts_descriptions:
                    new_title = generate_question_title(question_description, parts_descriptions)
                    print(f"Generated title: '{new_title}'")
                else:
                    print("Question has no content to generate title from.")
            
            print("✅ Database question tests completed!")
            
        except Exception as e:
            print(f"❌ Database question test failed: {e}")
            return False
        
        return True

def test_api_endpoints():
    """Test the API endpoints"""
    print("\n" + "="*50)
    print("API ENDPOINTS AVAILABLE:")
    print("="*50)
    print("1. POST /generate_question_title")
    print("   - Generates title from question description and parts")
    print("   - Body: {questionDescription: string, partsDescriptions: array}")
    print("   - Returns: {title: string}")
    print()
    print("2. POST /generate_title_for_question/<question_id>")
    print("   - Generates and saves title for existing question")
    print("   - Returns: {title: string, message: string}")
    print()
    print("3. Standalone function: generate_and_update_question_title(question_id)")
    print("   - Can be called directly from Python code")
    print("   - Updates question title in database")
    print("   - Returns: generated title string")
    print()
    print("4. Command Line Script: generate_title_by_id.py")
    print("   - Single question: python generate_title_by_id.py <question_id>")
    print("   - Range of questions: python generate_title_by_id.py <start_id> <end_id>")
    print("   - Examples:")
    print("     python generate_title_by_id.py 5")
    print("     python generate_title_by_id.py 1 10")

if __name__ == "__main__":
    print("🧪 Testing Automatic Question Title Generation")
    print("=" * 50)
    
    success = test_title_generation()
    test_api_endpoints()
    
    if success:
        print("\n✅ All tests passed! The title generation functionality is ready to use.")
        print("\nTo use in the web interface:")
        print("1. Go to edit any question")
        print("2. Click the 'Generate Title' button next to the title field")
        print("3. The AI will analyze the question content and generate an appropriate title")
        print("\nTo use the command line script:")
        print("- Single question: python generate_title_by_id.py 5")
        print("- Range of questions: python generate_title_by_id.py 1 10")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
